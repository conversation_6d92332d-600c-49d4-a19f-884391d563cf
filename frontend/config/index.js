import { defineConfig } from '@tarojs/cli'

import devConfig from './dev'
import testConfig from './test'
import prodConfig from './prod'

// https://taro-docs.jd.com/docs/next/config#defineconfig-辅助函数
export default defineConfig(async (merge, { command, mode }) => {
  const baseConfig = {
    projectName: '52kanduanju.mp',
    date: '2025-7-7',
    designWidth: 750,
    deviceRatio: {
      640: 2.34 / 2,
      750: 1,
      375: 2,
      828: 1.81 / 2
    },
    sourceRoot: 'src',
    outputRoot: 'dist',
    plugins: [
      "@tarojs/plugin-generator"
    ],
    defineConstants: {
      // API环境配置
      API_BASE_URL: (() => {
        const env = process.env.NODE_ENV || 'development'
        switch (env) {
          case 'development':
            return JSON.stringify('http://10.151.124.37:8080')
          case 'test':
            return JSON.stringify('https://test-52kdj-api.wansu.tech')
          case 'production':
            return JSON.stringify('https://52kdj-api.wansu.tech')
          default:
            return JSON.stringify('http://10.151.124.37:8080')
        }
      })(),
      // 环境标识
      NODE_ENV: JSON.stringify(process.env.NODE_ENV || 'development'),
      // 平台标识
      TARO_ENV: JSON.stringify(process.env.TARO_ENV || 'h5'),
      // 应用版本
      APP_VERSION: JSON.stringify(require('../package.json').version),
      // 是否为小程序环境
      IS_WEAPP: JSON.stringify(process.env.TARO_ENV === 'weapp')
    },
    copy: {
      patterns: [
        { from: 'src/assets/', to: 'dist/assets/' },
        { from: 'src/assets/fonts/', to: 'dist/fonts/' }
      ],
      options: {
      }
    },
    framework: 'vue3',
    compiler: 'vite',
    mini: {
      postcss: {
        pxtransform: {
          enable: true,
          config: {
          }
        },
        cssModules: {
          enable: false, // 默认为 false，如需使用 css modules 功能，则设为 true
          config: {
            namingPattern: 'module', // 转换模式，取值为 global/module
            generateScopedName: '[name]__[local]___[hash:base64:5]'
          }
        }
      },
      webpackChain(chain) {
        const { UnifiedWebpackPluginV5 } = require('weapp-tailwindcss/webpack')
        chain.merge({
          plugin: {
            install: {
              plugin: UnifiedWebpackPluginV5,
              args: [{
                appType: 'taro',
                rem2rpx: true
              }]
            }
          }
        })
      }
    },
    h5: {
      publicPath: '/',
      staticDirectory: 'static',
      router: {
        mode: 'hash'
      },
      miniCssExtractPluginOption: {
        ignoreOrder: true,
        filename: 'css/[name].[hash:8].css',
        chunkFilename: 'css/[name].[chunkhash:8].css'
      },
      postcss: {
        autoprefixer: {
          enable: true,
          config: {}
        },
        cssModules: {
          enable: false, // 默认为 false，如需使用 css modules 功能，则设为 true
          config: {
            namingPattern: 'module', // 转换模式，取值为 global/module
            generateScopedName: '[name]__[local]___[hash:base64:5]'
          }
        }
      },
      // Vite 构建优化配置
      vite: {
        build: {
          // 启用 CSS 代码分割
          cssCodeSplit: true,
          // 设置 chunk 大小警告限制
          chunkSizeWarningLimit: 1000
        },
        // 优化依赖预构建
        optimizeDeps: {
          include: [
            'vue',
            '@tarojs/taro',
            '@tarojs/components'
          ]
        }
      }
    },
    rn: {
      appName: 'taroDemo',
      postcss: {
        cssModules: {
          enable: false, // 默认为 false，如需使用 css modules 功能，则设为 true
        }
      }
    }
  }


  // 根据环境变量选择配置
  const env = process.env.NODE_ENV || 'development'

  switch (env) {
    case 'development':
      // 本地开发构建配置（不混淆压缩）
      return merge({}, baseConfig, devConfig)
    case 'test':
      // 测试环境构建配置（部分优化，保留调试信息）
      return merge({}, baseConfig, testConfig)
    case 'production':
      // 生产构建配置（完全优化压缩混淆）
      return merge({}, baseConfig, prodConfig)
    default:
      return merge({}, baseConfig, devConfig)
  }
})
