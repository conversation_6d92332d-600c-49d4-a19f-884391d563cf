.page-actor {
  min-height: 100vh;
  background-color: #ffffff;
  padding-bottom: 160rpx;
}

.main-content {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

// 演员信息区域
.actor-info {
  background: white;
  padding: 32rpx;
}

.actor-profile {
  display: flex;
  gap: 32rpx;
  margin-bottom: 32rpx;
}

.actor-avatar {
  width: 40%;
  aspect-ratio: 3/4;
  border-radius: 16rpx;
  overflow: hidden;
  flex-shrink: 0;
  background-color: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;

  .actor-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .avatar-placeholder {
    color: #999;
    font-size: 28rpx;
  }
}

.actor-details {
  width: 60%;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start; // 新增：让子元素靠左
  padding-top: 16rpx;
  gap: 16rpx;
}

.actor-name-row {
  display: flex;
  align-items: center;
}

.actor-name {
  font-size: 48rpx;
  font-weight: bold;
  color: #1f2937;
  margin-right: 16rpx;
}

.gender-badge {
  display: flex;
  align-items: center;
  padding: 4rpx 16rpx;
  border-radius: 9999px;
  font-size: 24rpx;
  font-weight: 500;

  &.gender-female {
    background-color: #fce7f3;
    color: #ec4899;
  }

  &.gender-male {
    background-color: #dbeafe;
    color: #3b82f6;
  }
}

.gender-icon {
  margin-right: 4rpx;
  font-size: 20rpx;
}

.gender-text {
  font-size: 24rpx;
}

.actor-heat {
  display: flex;
  align-items: center;
  color: #6b7280;
}

.heat-icon {
  font-size: 32rpx;
  color: #ef4444;
  margin-right: 8rpx;
  font-family: "Material Icons";
  font-weight: normal;
  font-style: normal;
  line-height: 1;
  letter-spacing: normal;
  text-transform: none;
  display: inline-block;
  white-space: nowrap;
  word-wrap: normal;
  direction: ltr;
  font-feature-settings: "liga";
  -webkit-font-feature-settings: "liga";
  -webkit-font-smoothing: antialiased;
}

.heat-text {
  font-size: 28rpx;
}

.actor-works {
  font-size: 28rpx;
  color: #6b7280;
}

.social-links {
  display: flex;
  gap: 16rpx;
  align-items: center;
  flex-wrap: wrap;
  margin-bottom: 24rpx;
}

.social-link {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  color: white;
  transition: all 0.3s ease;

  .social-icon {
    width: 32rpx;
    height: 32rpx;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.2);
  }

  .social-logo {
    font-size: 20rpx;
    font-weight: bold;
  }

  .social-name {
    font-size: 24rpx;
    font-weight: 500;
  }

  &.weibo {
    background-color: #e6162d;
  }

  &.douyin {
    background-color: #000;
  }

  &.xiaohongshu {
    background-color: #ff2442;
  }

  &.weixin {
    background-color: #07c160;
  }

  &:hover {
    opacity: 0.8;
    transform: translateY(-2rpx);
  }
}

// 按钮容器
.action-buttons {
  display: flex;
  gap: 16rpx;
  align-items: center;
}

.follow-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  background-color: transparent;
  color: #4f46e5;
  border: 2rpx solid #4f46e5;
  padding: 16rpx 24rpx;
  border-radius: 24rpx;
  font-size: 28rpx;
  font-weight: 600;
  transition: all 0.3s ease;
  flex: 1;

  .follow-icon {
    font-size: 28rpx;
  }

  .follow-text {
    font-weight: 600;
  }

  &.followed {
    background-color: #4f46e5;
    color: white;
    border-color: #4f46e5;

    .follow-icon {
      color: #ef4444; // 红色爱心
    }
  }

  &:hover {
    transform: translateY(-2rpx);
    box-shadow: 0 4rpx 12rpx 0 rgba(79, 70, 229, 0.2);
  }
}

.share-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  background-color: transparent;
  color: #6b7280;
  border: 2rpx solid #e5e7eb;
  padding: 16rpx 24rpx;
  border-radius: 24rpx;
  font-size: 28rpx;
  font-weight: 600;
  transition: all 0.3s ease;
  flex: 1;

  .share-icon {
    font-size: 28rpx;
  }

  .share-text {
    font-weight: 600;
  }

  &:hover {
    transform: translateY(-2rpx);
    box-shadow: 0 4rpx 12rpx 0 rgba(0, 0, 0, 0.1);
    border-color: #d1d5db;
  }
}

.follow-icon {
  font-size: 24rpx;
  font-family: "Material Icons";
  font-weight: normal;
  font-style: normal;
  line-height: 1;
  letter-spacing: normal;
  text-transform: none;
  display: inline-block;
  white-space: nowrap;
  word-wrap: normal;
  direction: ltr;
  font-feature-settings: "liga";
  -webkit-font-feature-settings: "liga";
  -webkit-font-smoothing: antialiased;
}

// 演员简介区域
.actor-bio-section {
  background: white;
  padding: 32rpx;
  border-top: 2rpx solid #f3f4f6;
}

.section-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #1f2937;
  margin-bottom: 16rpx;
  display: block;
}

.bio-content {
  font-size: 28rpx;
  color: #6b7280;
  line-height: 1.6;
  display: block;
}

// Tab区域样式
.tab-section {
  background: white;
  margin-top: 8rpx;
}

.tab-nav {
  display: flex;
  border-bottom: 2rpx solid #f3f4f6;
  background: white;
  position: sticky;
  top: 0;
  z-index: 10;
}

.tab-item {
  flex: 1;
  padding: 32rpx 16rpx;
  text-align: center;
  position: relative;
  transition: all 0.3s ease;
  cursor: pointer;

  .tab-text {
    font-size: 32rpx;
    font-weight: 600;
    color: #6b7280;
    transition: color 0.3s ease;
  }

  .tab-count {
    font-size: 24rpx;
    color: #9ca3af;
    margin-left: 4rpx;
  }

  &.tab-active {
    .tab-text {
      color: #4f46e5;
    }

    .tab-count {
      color: #4f46e5;
    }

    &::after {
      content: "";
      position: absolute;
      bottom: 0;
      left: 50%;
      transform: translateX(-50%);
      width: 60rpx;
      height: 4rpx;
      background-color: #4f46e5;
      border-radius: 2rpx;
    }
  }

  &:hover:not(.tab-active) {
    .tab-text {
      color: #374151;
    }
  }
}

.tab-content {
  min-height: 400rpx;
}

.tab-panel {
  padding: 32rpx;
}

// 短剧区域
.drama-section {
  background: white;
  padding: 32rpx;
  margin-top: 8rpx;
}

.drama-more {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  margin-top: 32rpx;
  padding: 16rpx;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    opacity: 0.8;
  }
}

.more-text {
  font-size: 28rpx;
  color: #4f46e5;
  text-align: center;
}

.more-icon {
  font-size: 32rpx;
  color: #4f46e5;
  font-family: "Material Icons";
  font-weight: normal;
  font-style: normal;
  line-height: 1;
  letter-spacing: normal;
  text-transform: none;
  display: inline-block;
  white-space: nowrap;
  word-wrap: normal;
  direction: ltr;
  font-feature-settings: "liga";
  -webkit-font-feature-settings: "liga";
  -webkit-font-smoothing: antialiased;
}

.drama-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 32rpx 24rpx;
  transition: all 0.5s ease;
}

.drama-item {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
  cursor: pointer;
  transition: transform 0.3s ease;

  &:hover {
    transform: scale(1.02);
  }
}

.drama-cover {
  position: relative;
  width: 100%;
  aspect-ratio: 3/4;
  border-radius: 16rpx;
  overflow: hidden;

  .drama-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

.drama-overlay {
  position: absolute;
  inset: 0;
  background-color: rgba(0, 0, 0, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;

  .play-icon {
    font-size: 60rpx;
    color: white;
    text-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.5);
    font-family: "Material Icons";
    font-weight: normal;
    font-style: normal;
    line-height: 1;
    letter-spacing: normal;
    text-transform: none;
    display: inline-block;
    white-space: nowrap;
    word-wrap: normal;
    direction: ltr;
    font-feature-settings: "liga";
    -webkit-font-feature-settings: "liga";
    -webkit-font-smoothing: antialiased;
  }
}

.drama-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #1f2937;
  line-height: 1.3;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

// Cut区域
.cut-section {
  background: white;
  border-radius: 24rpx;
  padding: 32rpx;
  box-shadow: 0 4rpx 12rpx 0 rgba(0, 0, 0, 0.05);
  margin-top: 8rpx;
}

// Cut网格容器
.cut-grid-container {
  position: relative;
}

.cut-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 32rpx 24rpx;
}

.cut-item {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
  cursor: pointer;
  transition: transform 0.3s ease;

  &:hover {
    transform: scale(1.02);
  }
}

.cut-cover {
  position: relative;
  width: 100%;
  aspect-ratio: 16/9;
  background-color: #e5e7eb;
  border-radius: 16rpx;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;

  .cover-text {
    color: #6b7280;
    font-size: 28rpx;
  }
}

.cut-overlay {
  position: absolute;
  inset: 0;
  display: flex;
  align-items: center;
  justify-content: center;

  .play-icon {
    font-size: 80rpx;
    color: white;
    text-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.5);
    font-family: "Material Icons";
    font-weight: normal;
    font-style: normal;
    line-height: 1;
    letter-spacing: normal;
    text-transform: none;
    display: inline-block;
    white-space: nowrap;
    word-wrap: normal;
    direction: ltr;
    font-feature-settings: "liga";
    -webkit-font-feature-settings: "liga";
    -webkit-font-smoothing: antialiased;
  }
}

.cut-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #1f2937;
  line-height: 1.3;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

// 整个Cut区域的付费解锁遮罩
.cut-area-pay-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.75);
  border-radius: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(8rpx);
  cursor: pointer;
  transition: all 0.3s ease;
  z-index: 10;

  &:hover {
    background: rgba(0, 0, 0, 0.8);
  }
}

.pay-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  color: white;
  padding: 32rpx;
}

.lock-icon {
  font-size: 120rpx;
  color: #f59e0b;
  margin-bottom: 24rpx;
  font-family: "Material Icons";
  font-weight: normal;
  font-style: normal;
  line-height: 1;
  letter-spacing: normal;
  text-transform: none;
  display: inline-block;
  white-space: nowrap;
  word-wrap: normal;
  direction: ltr;
  font-feature-settings: "liga";
  -webkit-font-feature-settings: "liga";
  -webkit-font-smoothing: antialiased;
}

.pay-text {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 16rpx;
}

.pay-subtitle {
  font-size: 24rpx;
  opacity: 0.8;
}

// 评论区域
.comment-section {
  background: white;
  border-radius: 24rpx;
  padding: 32rpx;
  box-shadow: 0 4rpx 12rpx 0 rgba(0, 0, 0, 0.05);
  margin-top: 8rpx;
}

.comment-input {
  display: flex;
  align-items: center;
  gap: 16rpx;
  margin-bottom: 32rpx;
  padding: 12rpx 20rpx; // 减少内边距，降低高度
  background-color: #f9fafb;
  border-radius: 40rpx; // 减少圆角，配合较低的高度
  border: 2rpx solid #e5e7eb;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    border-color: #6366f1;
    background-color: #f0f9ff;
  }
}

.comment-field-placeholder {
  flex: 1;

  .placeholder-text {
    color: #9ca3af;
    font-size: 28rpx;
  }
}

.comment-submit {
  background-color: #4f46e5;
  color: white;
  padding: 16rpx 32rpx;
  border-radius: 9999px;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    background-color: #4338ca;
  }

  &:active {
    transform: scale(0.95);
  }
}

.submit-text {
  font-size: 28rpx;
  font-weight: 600;
  color: white;
}

.comment-list {
  display: flex;
  flex-direction: column;
  gap: 32rpx;
  margin-bottom: 32rpx;
}

.comment-item {
  display: flex;
  align-items: flex-start;
  gap: 24rpx;
}

.comment-avatar {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  background-color: #d1d5db;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;

  .avatar-text {
    color: #6b7280;
    font-size: 20rpx;
  }
}

.comment-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.comment-author {
  font-size: 28rpx;
  font-weight: 600;
  color: #374151;
}

.comment-text {
  font-size: 28rpx;
  color: #1f2937;
  line-height: 1.5;
}

.comment-time {
  font-size: 24rpx;
  color: #9ca3af;
}

.comment-more {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  padding: 16rpx;
  cursor: pointer;
  transition: opacity 0.3s ease;

  &:hover {
    opacity: 0.7;
  }
}

.more-text {
  font-size: 28rpx;
  color: #4f46e5;
}

.more-icon {
  font-size: 32rpx;
  color: #4f46e5;
  font-family: "Material Icons";
  font-weight: normal;
  font-style: normal;
  line-height: 1;
  letter-spacing: normal;
  text-transform: none;
  display: inline-block;
  white-space: nowrap;
  word-wrap: normal;
  direction: ltr;
  font-feature-settings: "liga";
  -webkit-font-feature-settings: "liga";
  -webkit-font-smoothing: antialiased;
}

// 评论弹窗
.comment-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  align-items: flex-end;
  justify-content: center;
}

.comment-modal {
  width: 100%;
  max-width: 750rpx;
  background: white;
  border-radius: 32rpx 32rpx 0 0;
  padding: 32rpx;
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 32rpx;
  padding-bottom: 24rpx;
  border-bottom: 2rpx solid #f3f4f6;
}

.modal-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #1f2937;
}

.modal-close {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: #f3f4f6;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    background: #e5e7eb;
  }

  .material-icons {
    font-size: 32rpx;
    color: #6b7280;
  }
}

.modal-content {
  margin-bottom: 32rpx;
}

.comment-textarea {
  width: 100%;
  min-height: 200rpx;
  padding: 24rpx;
  border: 2rpx solid #e5e7eb;
  border-radius: 16rpx;
  font-size: 32rpx;
  line-height: 1.5;
  resize: none;
  outline: none;
  background: #fafafa;

  &:focus {
    border-color: #6366f1;
    background: white;
  }
}

.comment-count {
  text-align: right;
  margin-top: 16rpx;
  font-size: 24rpx;
  color: #9ca3af;
}

.modal-footer {
  display: flex;
  gap: 24rpx;
}

.modal-cancel,
.modal-submit {
  flex: 1;
  padding: 20rpx;
  border-radius: 48rpx;
  text-align: center;
  font-size: 32rpx;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.modal-cancel {
  background: #f3f4f6;
  color: #6b7280;

  &:hover {
    background: #e5e7eb;
  }
}

.modal-submit {
  background: #4f46e5;
  color: white;

  &:hover {
    background: #4338ca;
  }

  &:active {
    transform: scale(0.95);
  }
}

// 底部固定按钮
.page-actor .bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  max-width: 750rpx;
  margin: 0 auto;
  background: white;
  border-top: 2rpx solid #f3f4f6;
  padding: 32rpx;
  display: flex;
  justify-content: center;
  z-index: 100;
}

.page-actor .action-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
  padding: 20rpx 40rpx;
  border-radius: 9999px;
  font-weight: bold;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 28rpx;
  width: 50%;
  max-width: 300rpx;

  &:hover {
    transform: translateY(-2rpx);
  }
}

.page-actor .share-button {
  background: linear-gradient(45deg, #4f46e5, #f59e0b);
  color: white;
  width: auto;
  margin: 0;

  &:hover {
    opacity: 0.9;
  }
}
