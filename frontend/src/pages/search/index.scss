.page-search {
  min-height: 100vh;
  background: #ffffff;
  box-sizing: border-box;
  padding-bottom: 120rpx;
}
.header {
  padding: 24rpx 32rpx 16rpx 32rpx;
  background: #fff;
  position: sticky;
  top: 0;
  z-index: 10;
}
.search-row {
  display: flex;
  align-items: center;
  gap: 16rpx;
}
.back-btn {
  color: #64748b;
  font-size: 36rpx;
  padding: 8rpx;
}
.iconfont {
  font-family: "Material Icons", "iconfont", sans-serif;
}
.icon-arrow_back_ios_new::before {
  content: "arrow_back_ios_new";
}
.icon-search::before {
  content: "search";
}
.icon-chevron_right::before {
  content: "chevron_right";
}
.icon-play_circle_outline::before {
  content: "play_circle_outline";
}
.search-input-wrap {
  position: relative;
  flex: 1;
  display: flex;
  align-items: center;
}
.search-icon {
  position: absolute;
  left: 24rpx;
  color: #a1a1aa;
  font-size: 32rpx;
  z-index: 1;
}
.search-input {
  width: 100%;
  background: #f3f4f6;
  border-radius: 999rpx;
  padding: 16rpx 32rpx 16rpx 64rpx;
  font-size: 28rpx;
  border: none;
  outline: none;
  color: #2d3748;
}
.search-btn {
  margin-left: 8rpx;
}
.search-btn-text {
  color: #4f46e5;
  font-weight: bold;
  font-size: 28rpx;
}
.search-main {
  padding: 0 32rpx 32rpx 32rpx;
}
.section {
  background: #fff;
  border-radius: 20rpx;
  margin-top: 32rpx;
  padding: 32rpx 0 0 0;
  box-shadow: 0 2rpx 8rpx 0 #e0e7ff11;
}
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 0 32rpx 24rpx 32rpx;
}
.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #2d3748;
}
.section-more {
  display: flex;
  align-items: center;
  color: #64748b;
  font-size: 26rpx;
}
.section-more-text {
  margin-right: 4rpx;
}
.section-more-icon {
  font-size: 24rpx;
}
.actor-row {
  display: flex;
  align-items: flex-start;
  gap: 24rpx;
  margin: 0 32rpx 32rpx 32rpx;
  padding: 16rpx 0;
  border-bottom: 1rpx solid #f1f5f9;

  &:last-child {
    border-bottom: none;
    margin-bottom: 16rpx;
  }
}
.actor-cover {
  width: 96rpx;
  height: 128rpx;
  background: #e5e7eb;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.cover-text {
  color: #6b7280;
  font-size: 24rpx;
}

.cover-image {
  width: 100%;
  height: 100%;
  border-radius: 12rpx;
}
.actor-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}
.actor-name {
  font-size: 28rpx;
  font-weight: bold;
  color: #2d3748;
}
.actor-desc {
  font-size: 24rpx;
  color: #64748b;
}
.drama-list {
  display: flex;
  flex-direction: column;
  gap: 32rpx;
  margin: 0 32rpx 32rpx 32rpx;
}
.drama-row {
  display: flex;
  align-items: stretch;
  gap: 24rpx;
}
.drama-cover {
  width: 96rpx;
  min-height: 128rpx;
  background: #e5e7eb;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}
.drama-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
  min-height: 128rpx;
  justify-content: space-between;
}
.drama-name {
  font-size: 28rpx;
  font-weight: bold;
  color: #2d3748;
}
.primary {
  color: #4f46e5;
}
.drama-meta {
  font-size: 24rpx;
  color: #64748b;
}
.drama-tags {
  display: flex;
  gap: 12rpx;
  margin-top: 4rpx;
}
.drama-tag {
  background: #e5e7eb;
  color: #64748b;
  font-size: 20rpx;
  padding: 4rpx 16rpx;
  border-radius: 999rpx;
}
.cut-list {
  display: flex;
  flex-direction: column;
  gap: 32rpx;
  margin: 0 32rpx 32rpx 32rpx;
}
.cut-row {
  display: flex;
  align-items: stretch;
  gap: 24rpx;
}
.cut-cover {
  width: 96rpx;
  min-height: 128rpx;
  background: #e5e7eb;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
  flex-shrink: 0;
}
.cut-play {
  position: absolute;
  inset: 0;
  background: rgba(0, 0, 0, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
}
.cut-play .iconfont {
  color: #fff;
  font-size: 40rpx;
}
.cut-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
  min-height: 128rpx;
  justify-content: space-between;
}
.cut-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #2d3748;
}
.cut-meta {
  font-size: 24rpx;
  color: #64748b;
}
.news-list {
  display: flex;
  flex-direction: column;
  gap: 32rpx;
  margin: 0 32rpx 0 32rpx;
}
.news-item {
  border-bottom: 1rpx solid #f1f5f9;
  padding-bottom: 24rpx;
}
.news-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #2d3748;
  margin-bottom: 8rpx;
  display: block;
}
.news-desc {
  font-size: 24rpx;
  color: #64748b;
  margin-bottom: 8rpx;
  display: block;
}
.news-meta {
  font-size: 20rpx;
  color: #a1a1aa;
  display: block;
}
