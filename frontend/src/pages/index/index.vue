<template>
  <view class="page-index">
    <!-- 今日推荐 -->
    <view class="today-recommend">
      <view class="today-row">
        <text class="today-title">今日运势</text>
        <text class="today-date">{{ homeData.todayFortune.date || today }}</text>
      </view>
      <view class="today-grid">
        <view class="today-item good">
          <text class="good-title">宜</text>
          <view class="good-desc">
            <text class="desc-text">{{ homeData.todayFortune.good }}</text>
          </view>
        </view>
        <view class="today-item bad">
          <text class="bad-title">忌</text>
          <view class="bad-desc">
            <text class="desc-text">{{ homeData.todayFortune.bad }}</text>
          </view>
        </view>
      </view>
    </view>
    <!-- 今日最火 -->
    <view class="hot-section">
      <view class="hot-header">
        <text class="hot-title">🔥 今日最火</text>
        <text class="material-icons hot-more" @tap="onDiscoverClick">chevron_right</text>
      </view>
      <!-- 短剧网格布局 2列6行 -->
      <view class="hot-dramas-grid">
        <view v-for="(drama, index) in homeData.dramas.slice(0, 12)" :key="index" class="hot-drama-item"
          @tap="onDramaClick(drama.drama)">
          <view class="hot-drama-cover">
            <image v-if="getDramaCover(drama.drama)" :src="getDramaCover(drama.drama)" class="cover-image"
              mode="aspectFill" />
            <text v-else class="cover-text">封面</text>
            <text v-if="getDramaTag(drama.drama)" class="hot-tag tag-red">
              {{ getDramaTag(drama.drama) }}
            </text>
          </view>
          <text class="hot-drama-name">{{ getDramaTitle(drama.drama) }}</text>
          <text class="hot-drama-info">{{ getDramaHeat(drama.drama) }}</text>
        </view>
      </view>
    </view>
    <view class="discover-btn-wrap">
      <button class="discover-btn" @tap="onDiscoverClick">发现更多精彩短剧</button>
    </view>
  </view>
</template>
<script setup>
import './index.scss'
import Taro from '@tarojs/taro'
import { ref, onMounted } from 'vue'
import { api } from '../../utils/api'

// 响应式数据
const homeData = ref({
  todayFortune: {
    date: '',
    good: '看甜甜的恋爱',
    bad: '沉溺于痛苦'
  },
  dramas: []
})

const loading = ref(false)

// Taro + Vue3 获取今天日期，格式 MM/DD
const today = (() => {
  const d = new Date()
  const mm = String(d.getMonth() + 1).padStart(2, '0')
  const dd = String(d.getDate()).padStart(2, '0')
  return `${mm}/${dd}`
})()

// 加载首页数据
const loadHomeData = async () => {
  try {
    loading.value = true
    const response = await api.getHomeRecommendation()
    if (response.code === 200) {
      // 根据最新API返回结构处理数据
      const data = response.data

      // 处理今日运势数据
      if (data.todayFortune) {
        homeData.value.todayFortune = {
          date: data.todayFortune.date || today,
          good: data.todayFortune.good || '看甜甜的恋爱',
          bad: data.todayFortune.bad || '沉溺于痛苦'
        }
      }

      // 处理推荐内容数据
      if (data.dramas && Array.isArray(data.dramas)) {
        homeData.value.dramas = data.dramas.map(item => ({
          drama: {
            id: item.id || '',
            bookName: item.bookName || '未知短剧',
            cover: item.cover || '',
            heatCount: item.heatCount || item.readCount || 0,
            categories: item.categories || []
          }
        }))
      }
    }
  } catch (error) {
    console.error('加载首页数据失败:', error)
    Taro.showToast({
      title: '加载数据失败',
      icon: 'none'
    })
  } finally {
    loading.value = false
  }
}

// 跳转到短剧详情页
function onDramaClick(drama) {
  console.log('跳转到短剧详情页:', drama)
  const dramaId = typeof drama === 'string' ? drama : drama.id
  const title = typeof drama === 'string' ? drama : drama.title
  Taro.navigateTo({
    url: `/pages/drama/index?id=${dramaId}&title=${encodeURIComponent(title)}`
  })
}



// 跳转到找好剧页面（tabbar页面）- 热剧榜
function onDiscoverClick() {
  console.log('跳转到找好剧页面-热剧榜')
  Taro.switchTab({
    url: '/pages/ranking/index'
  })
}

// 辅助函数：获取短剧封面
const getDramaCover = (drama) => {
  return drama?.cover || null
}

// 辅助函数：获取短剧标题
const getDramaTitle = (drama) => {
  return drama?.bookName || drama?.title || '未知短剧'
}

// 辅助函数：获取短剧标签
const getDramaTag = (drama) => {
  if (drama?.categories && Array.isArray(drama.categories) && drama.categories.length > 0) {
    return drama.categories[0].name || drama.categories[0]
  }
  return null
}

// 辅助函数：获取短剧热度
const getDramaHeat = (drama) => {
  const count = drama?.heatCount || drama?.readCount || 0
  if (count > 0) {
    if (count >= 10000) {
      return `${Math.floor(count / 10000)}万人正在看`
    } else {
      return `${count}人正在看`
    }
  }
  return '热度未知'
}



// 页面加载时获取数据
onMounted(() => {
  loadHomeData()
})
</script>
