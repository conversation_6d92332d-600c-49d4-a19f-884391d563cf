/* Tailwind CSS 基础样式已通过 weapp-tailwindcss 插件处理 */

// 本地 Material Icons 字体
@import "./assets/fonts/material-icons.css";

// 全局CSS变量定义
:root {
  --primary-color: #4f46e5;
  --secondary-color: #f59e0b;
  --background-color: #ffffff;
  --text-primary: #1f2937;
  --text-secondary: #6b7280;
  --border-color: #e5e7eb;
}

// 全局重置和基础样式
* {
  box-sizing: border-box;
}

// 页面容器样式
page {
  background-color: var(--background-color);
  min-height: 100vh;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto",
    "Helvetica Neue", Arial, sans-serif;
}

// 兼容H5环境的body样式
body {
  background-color: var(--background-color);
  min-height: max(884px, 100dvh);
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto",
    "Helvetica Neue", Arial, sans-serif;
}
