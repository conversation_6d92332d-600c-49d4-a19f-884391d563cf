package routes

import (
	"github.com/gin-gonic/gin"
	"github.com/jony4/52kanduanju.mp/server/config"
	"github.com/jony4/52kanduanju.mp/server/middleware"
	"gorm.io/gorm"
)

func SetupRoutes(router *gin.Engine, db *gorm.DB, cfg *config.Config) {
	// 创建控制器实例
	ctrl := NewControllers(db, cfg)

	// 全局中间件
	router.Use(middleware.CORS())
	router.Use(middleware.Logger(cfg))
	router.Use(middleware.Recovery())

	// 健康检查
	router.GET("/health", func(c *gin.Context) {
		c.JSON(200, gin.H{"status": "ok", "message": "Server is running"})
	})

	// API v1 routes
	v1 := router.Group("/v1")
	{
		// Recommendation - 推荐内容
		v1.GET("/recommendation/home", ctrl.GetHomeRecommendation)

		// Search - 搜索功能
		v1.GET("/search", ctrl.Search)

		// Dramas - 短剧相关
		v1.GET("/dramas", ctrl.GetDramas)
		v1.GET("/dramas/:dramaId", ctrl.GetDramaByID)
		v1.GET("/dramas/:dramaId/cuts", middleware.OptionalAuth(), ctrl.GetDramaCuts)
		v1.GET("/dramas/:dramaId/news", ctrl.GetDramaNews)

		// Actors - 演员相关
		v1.GET("/actors/:actorId", middleware.OptionalAuth(), ctrl.GetActorByID)
		v1.GET("/actors/:actorId/dramas", ctrl.GetActorDramas)
		v1.GET("/actors/:actorId/cuts", middleware.OptionalAuth(), ctrl.GetActorCuts)

		// 需要认证的演员操作
		actorAuth := v1.Group("/actors/:actorId")
		actorAuth.Use(middleware.AuthRequired())
		{
			actorAuth.POST("/follow", ctrl.FollowActor)
			actorAuth.DELETE("/follow", ctrl.UnfollowActor)
		}

		// Comments - 评论系统
		v1.GET("/comments", ctrl.GetComments)
		v1.POST("/comments", middleware.AuthRequired(), ctrl.PostComment)

		// Rankings - 排行榜
		v1.GET("/ranking/tags", ctrl.GetTagRanking)
		v1.GET("/ranking/actors", ctrl.GetActorRanking)

		// Tags - 标签系统
		v1.GET("/tags", ctrl.GetTags)
		v1.GET("/tags/:tagName", ctrl.GetTagByName)
		v1.GET("/tags/:tagName/dramas", ctrl.GetTagDramas)

		// User - 用户相关（需要认证）
		userAuth := v1.Group("/user")
		userAuth.Use(middleware.AuthRequired())
		{
			userAuth.GET("/follows", ctrl.GetUserFollows)
		}

		// Payment - 付费系统（需要认证）
		paymentAuth := v1.Group("/payment")
		paymentAuth.Use(middleware.AuthRequired())
		{
			paymentAuth.POST("/unlock", ctrl.UnlockPayment)
			paymentAuth.GET("/status", ctrl.GetPaymentStatus)
		}
	}
}
