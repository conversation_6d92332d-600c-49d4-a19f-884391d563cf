package middleware

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/jony4/52kanduanju.mp/server/config"
)

// LogEntry represents a structured log entry
type LogEntry struct {
	RequestID    string                 `json:"request_id"`
	Timestamp    string                 `json:"timestamp"`
	Level        string                 `json:"level"`
	Type         string                 `json:"type"` // "request" or "response"
	Method       string                 `json:"method,omitempty"`
	URL          string                 `json:"url,omitempty"`
	Path         string                 `json:"path,omitempty"`
	ClientIP     string                 `json:"client_ip,omitempty"`
	UserAgent    string                 `json:"user_agent,omitempty"`
	Headers      map[string]interface{} `json:"headers,omitempty"`
	Body         interface{}            `json:"body,omitempty"`
	StatusCode   int                    `json:"status_code,omitempty"`
	ResponseTime string                 `json:"response_time,omitempty"`
	Error        string                 `json:"error,omitempty"`
}

// ResponseWriter wrapper to capture response data
type responseWriter struct {
	gin.ResponseWriter
	body *bytes.Buffer
}

func (w *responseWriter) Write(b []byte) (int, error) {
	w.body.Write(b)
	return w.ResponseWriter.Write(b)
}

// Logger creates a comprehensive logging middleware
func Logger(cfg *config.Config) gin.HandlerFunc {
	if !cfg.Logging.Enabled {
		// Return basic logger if detailed logging is disabled
		return gin.Logger()
	}

	return func(c *gin.Context) {
		// Generate request ID for correlation
		requestID := generateRequestID()
		c.Set("request_id", requestID)

		start := time.Now()

		// Log request
		logRequest(c, cfg, requestID)

		// Wrap response writer to capture response data
		responseBody := &bytes.Buffer{}
		writer := &responseWriter{
			ResponseWriter: c.Writer,
			body:           responseBody,
		}
		c.Writer = writer

		// Process request
		c.Next()

		// Log response
		logResponse(c, cfg, requestID, start, responseBody)
	}
}

// logRequest logs incoming HTTP request details
func logRequest(c *gin.Context, cfg *config.Config, requestID string) {
	if !shouldLog(cfg.Logging.Level, "info") {
		return
	}

	entry := LogEntry{
		RequestID: requestID,
		Timestamp: time.Now().Format(time.RFC3339),
		Level:     "info",
		Type:      "request",
		Method:    c.Request.Method,
		URL:       c.Request.URL.String(),
		Path:      c.Request.URL.Path,
		ClientIP:  c.ClientIP(),
		UserAgent: c.Request.UserAgent(),
	}

	// Log headers if enabled
	if cfg.Logging.LogHeaders {
		headers := make(map[string]interface{})
		for key, values := range c.Request.Header {
			if len(values) == 1 {
				headers[key] = maskSensitiveData(key, values[0], cfg.Logging.SensitiveFields)
			} else {
				maskedValues := make([]string, len(values))
				for i, value := range values {
					maskedValues[i] = maskSensitiveData(key, value, cfg.Logging.SensitiveFields)
				}
				headers[key] = maskedValues
			}
		}
		entry.Headers = headers
	}

	// Log request body if enabled and present
	if cfg.Logging.LogRequestBody && c.Request.Body != nil {
		bodyBytes, err := io.ReadAll(c.Request.Body)
		if err == nil && len(bodyBytes) > 0 {
			// Restore the body for further processing
			c.Request.Body = io.NopCloser(bytes.NewBuffer(bodyBytes))

			// Limit body size for logging
			if len(bodyBytes) > cfg.Logging.MaxBodySize {
				bodyBytes = bodyBytes[:cfg.Logging.MaxBodySize]
			}

			// Try to parse as JSON, otherwise log as string
			var bodyData interface{}
			if json.Unmarshal(bodyBytes, &bodyData) == nil {
				entry.Body = maskSensitiveDataInJSON(bodyData, cfg.Logging.SensitiveFields)
			} else {
				entry.Body = string(bodyBytes)
			}
		}
	}

	logEntry(entry, cfg.Logging.PrettyPrint)
}

// logResponse logs outgoing HTTP response details
func logResponse(c *gin.Context, cfg *config.Config, requestID string, start time.Time, responseBody *bytes.Buffer) {
	if !shouldLog(cfg.Logging.Level, "info") {
		return
	}

	duration := time.Since(start)
	entry := LogEntry{
		RequestID:    requestID,
		Timestamp:    time.Now().Format(time.RFC3339),
		Level:        "info",
		Type:         "response",
		StatusCode:   c.Writer.Status(),
		ResponseTime: duration.String(),
	}

	// Add error information if present
	if len(c.Errors) > 0 {
		entry.Error = c.Errors.String()
		entry.Level = "error"
	}

	// Log response headers if enabled
	if cfg.Logging.LogHeaders {
		headers := make(map[string]interface{})
		for key, values := range c.Writer.Header() {
			if len(values) == 1 {
				headers[key] = maskSensitiveData(key, values[0], cfg.Logging.SensitiveFields)
			} else {
				maskedValues := make([]string, len(values))
				for i, value := range values {
					maskedValues[i] = maskSensitiveData(key, value, cfg.Logging.SensitiveFields)
				}
				headers[key] = maskedValues
			}
		}
		entry.Headers = headers
	}

	// Log response body if enabled
	if cfg.Logging.LogResponseBody && responseBody.Len() > 0 {
		bodyBytes := responseBody.Bytes()

		// Limit body size for logging
		if len(bodyBytes) > cfg.Logging.MaxBodySize {
			bodyBytes = bodyBytes[:cfg.Logging.MaxBodySize]
		}

		// Try to parse as JSON, otherwise log as string
		var bodyData interface{}
		if json.Unmarshal(bodyBytes, &bodyData) == nil {
			entry.Body = maskSensitiveDataInJSON(bodyData, cfg.Logging.SensitiveFields)
		} else {
			entry.Body = string(bodyBytes)
		}
	}

	logEntry(entry, cfg.Logging.PrettyPrint)
}

// Utility functions

// generateRequestID generates a unique request ID for correlation
func generateRequestID() string {
	return fmt.Sprintf("%d", time.Now().UnixNano())
}

// shouldLog determines if a message should be logged based on log level
func shouldLog(configLevel, messageLevel string) bool {
	levels := map[string]int{
		"debug": 0,
		"info":  1,
		"warn":  2,
		"error": 3,
	}

	configLevelInt, exists := levels[strings.ToLower(configLevel)]
	if !exists {
		configLevelInt = 1 // default to info
	}

	messageLevelInt, exists := levels[strings.ToLower(messageLevel)]
	if !exists {
		messageLevelInt = 1 // default to info
	}

	return messageLevelInt >= configLevelInt
}

// maskSensitiveData masks sensitive information in string values
func maskSensitiveData(key, value string, sensitiveFields []string) string {
	keyLower := strings.ToLower(key)
	for _, field := range sensitiveFields {
		if strings.Contains(keyLower, strings.ToLower(field)) {
			if len(value) <= 4 {
				return "***"
			}
			return value[:2] + strings.Repeat("*", len(value)-4) + value[len(value)-2:]
		}
	}
	return value
}

// maskSensitiveDataInJSON recursively masks sensitive data in JSON structures
func maskSensitiveDataInJSON(data interface{}, sensitiveFields []string) interface{} {
	switch v := data.(type) {
	case map[string]interface{}:
		result := make(map[string]interface{})
		for key, value := range v {
			if isSensitiveField(key, sensitiveFields) {
				if str, ok := value.(string); ok {
					result[key] = maskString(str)
				} else {
					result[key] = "***"
				}
			} else {
				result[key] = maskSensitiveDataInJSON(value, sensitiveFields)
			}
		}
		return result
	case []interface{}:
		result := make([]interface{}, len(v))
		for i, item := range v {
			result[i] = maskSensitiveDataInJSON(item, sensitiveFields)
		}
		return result
	default:
		return v
	}
}

// isSensitiveField checks if a field name is considered sensitive
func isSensitiveField(fieldName string, sensitiveFields []string) bool {
	fieldLower := strings.ToLower(fieldName)
	for _, field := range sensitiveFields {
		if strings.Contains(fieldLower, strings.ToLower(field)) {
			return true
		}
	}
	return false
}

// maskString masks a string value
func maskString(value string) string {
	if len(value) <= 4 {
		return "***"
	}
	return value[:2] + strings.Repeat("*", len(value)-4) + value[len(value)-2:]
}

// logEntry outputs the log entry in the specified format
func logEntry(entry LogEntry, prettyPrint bool) {
	var output []byte
	var err error

	if prettyPrint {
		output, err = json.MarshalIndent(entry, "", "  ")
	} else {
		output, err = json.Marshal(entry)
	}

	if err != nil {
		log.Printf("Error marshaling log entry: %v", err)
		return
	}

	log.Println(string(output))
}

// Recovery 自定义恢复中间件
func Recovery() gin.HandlerFunc {
	return gin.CustomRecovery(func(c *gin.Context, recovered interface{}) {
		if err, ok := recovered.(string); ok {
			c.String(500, fmt.Sprintf("error: %s", err))
		}
		c.AbortWithStatus(500)
	})
}
