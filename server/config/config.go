package config

import (
	"log"
	"os"
	"strconv"

	"github.com/joho/godotenv"
)

type Config struct {
	Database DatabaseConfig `json:"database"`
	Server   ServerConfig   `json:"server"`
	App      AppConfig      `json:"app"`
	JWT      JWTConfig      `json:"jwt"`
	Logging  LoggingConfig  `json:"logging"`
}

type DatabaseConfig struct {
	Host     string `json:"host"`
	Port     int    `json:"port"`
	User     string `json:"user"`
	Password string `json:"password"`
	DBName   string `json:"db_name"`
	Charset  string `json:"charset"`
}

type ServerConfig struct {
	Port string `json:"port"`
	Mode string `json:"mode"` // debug, release, test
}

type AppConfig struct {
	Name            string `json:"name"`
	Version         string `json:"version"`
	DefaultPageSize int    `json:"default_page_size"`
	MaxPageSize     int    `json:"max_page_size"`
}

type JWTConfig struct {
	Secret       string `json:"secret"`
	ExpireHours  int    `json:"expire_hours"`
}

type LoggingConfig struct {
	Enabled           bool     `json:"enabled"`
	Level             string   `json:"level"`              // debug, info, warn, error
	LogRequestBody    bool     `json:"log_request_body"`
	LogResponseBody   bool     `json:"log_response_body"`
	LogHeaders        bool     `json:"log_headers"`
	MaxBodySize       int      `json:"max_body_size"`      // Maximum body size to log in bytes
	SensitiveFields   []string `json:"sensitive_fields"`   // Fields to mask in logs
	PrettyPrint       bool     `json:"pretty_print"`       // Pretty print JSON logs
}

var GlobalConfig *Config

func LoadConfig() *Config {
	// Load .env file if exists
	if err := godotenv.Load(); err != nil {
		log.Println("No .env file found, using environment variables")
	}

	config := &Config{
		Database: DatabaseConfig{
			Host:     getEnv("DB_HOST", "localhost"),
			Port:     getEnvAsInt("DB_PORT", 3306),
			User:     getEnv("DB_USER", "root"),
			Password: getEnv("DB_PASSWORD", ""),
			DBName:   getEnv("DB_NAME", "kanduanju"),
			Charset:  getEnv("DB_CHARSET", "utf8mb4"),
		},
		Server: ServerConfig{
			Port: getEnv("SERVER_PORT", "8080"),
			Mode: getEnv("GIN_MODE", "debug"),
		},
		App: AppConfig{
			Name:            getEnv("APP_NAME", "我爱看短剧"),
			Version:         getEnv("APP_VERSION", "1.0.0"),
			DefaultPageSize: getEnvAsInt("DEFAULT_PAGE_SIZE", 20),
			MaxPageSize:     getEnvAsInt("MAX_PAGE_SIZE", 50),
		},
		JWT: JWTConfig{
			Secret:      getEnv("JWT_SECRET", "your_jwt_secret_key"),
			ExpireHours: getEnvAsInt("JWT_EXPIRE_HOURS", 24),
		},
		Logging: LoggingConfig{
			Enabled:         getEnvAsBool("LOG_ENABLED", true),
			Level:           getEnv("LOG_LEVEL", "info"),
			LogRequestBody:  getEnvAsBool("LOG_REQUEST_BODY", true),
			LogResponseBody: getEnvAsBool("LOG_RESPONSE_BODY", true),
			LogHeaders:      getEnvAsBool("LOG_HEADERS", true),
			MaxBodySize:     getEnvAsInt("LOG_MAX_BODY_SIZE", 10240), // 10KB default
			SensitiveFields: []string{"password", "token", "secret", "key", "authorization", "auth", "jwt"},
			PrettyPrint:     getEnvAsBool("LOG_PRETTY_PRINT", true),
		},
	}

	GlobalConfig = config
	return config
}

func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

func getEnvAsInt(key string, defaultValue int) int {
	if value := os.Getenv(key); value != "" {
		if intValue, err := strconv.Atoi(value); err == nil {
			return intValue
		}
	}
	return defaultValue
}

func getEnvAsBool(key string, defaultValue bool) bool {
	if value := os.Getenv(key); value != "" {
		if boolValue, err := strconv.ParseBool(value); err == nil {
			return boolValue
		}
	}
	return defaultValue
}

func (c *Config) GetDSN() string {
	return c.Database.User + ":" + c.Database.Password + "@tcp(" + c.Database.Host + ":" + strconv.Itoa(c.Database.Port) + ")/" + c.Database.DBName + "?charset=" + c.Database.Charset + "&parseTime=True&loc=Local"
}
